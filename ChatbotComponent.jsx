import React, { useState, useEffect, useRef } from 'react';
import './Chatbot.css';

const ChatbotComponent = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [isMinimized, setIsMinimized] = useState(false);
  const [messages, setMessages] = useState([]);
  const [inputValue, setInputValue] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [suggestions, setSuggestions] = useState([]);
  const [showContactModal, setShowContactModal] = useState(false);
  const [conversationState, setConversationState] = useState('welcome');
  const [userContext, setUserContext] = useState({});

  const messagesEndRef = useRef(null);

  useEffect(() => {
    if (isOpen && messages.length === 0) {
      addWelcomeMessage();
    }
  }, [isOpen]);

  useEffect(() => {
    scrollToBottom();
  }, [messages, isTyping]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const addWelcomeMessage = () => {
    const welcomeMessage = {
      id: Date.now(),
      content: `👋 Hi there! Welcome to TechnoloWay!

I'm Alex, your virtual assistant. I'm here to help you learn about our software development services and find the perfect solution for your project.

What brings you here today?`,
      sender: 'bot',
      timestamp: new Date(),
      quickActions: [
        { label: 'Our Services', value: 'services', icon: 'fas fa-code' },
        { label: 'Get Quote', value: 'quote', icon: 'fas fa-calculator' },
        { label: 'Portfolio', value: 'portfolio', icon: 'fas fa-briefcase' },
        { label: 'Talk to Human', value: 'human', icon: 'fas fa-user' }
      ]
    };

    setMessages([welcomeMessage]);
  };

  const addMessage = (content, sender, options = {}) => {
    const message = {
      id: Date.now(),
      content,
      sender,
      timestamp: new Date(),
      ...options
    };

    setMessages(prev => [...prev, message]);

    if (options.suggestions) {
      setSuggestions(options.suggestions);
      setShowSuggestions(true);
    } else {
      setShowSuggestions(false);
    }
  };

  const handleSendMessage = () => {
    if (!inputValue.trim()) return;

    addMessage(inputValue, 'user');
    const userMessage = inputValue;
    setInputValue('');

    // Show typing indicator
    setIsTyping(true);

    // Simulate processing time
    setTimeout(() => {
      setIsTyping(false);
      processUserMessage(userMessage);
    }, 1000 + Math.random() * 1000);
  };

  const processUserMessage = (message) => {
    const response = generateResponse(message);
    addMessage(response.content, 'bot', response.options);
  };

  const generateResponse = (message) => {
    const lowerMessage = message.toLowerCase();

    if (containsKeywords(lowerMessage, ['service', 'services', 'what do you do'])) {
      return getServicesResponse();
    }

    if (containsKeywords(lowerMessage, ['quote', 'price', 'cost', 'budget'])) {
      return getQuoteResponse();
    }

    if (containsKeywords(lowerMessage, ['portfolio', 'work', 'projects'])) {
      return getPortfolioResponse();
    }

    if (containsKeywords(lowerMessage, ['human', 'person', 'agent', 'talk'])) {
      return getHumanHandoffResponse();
    }

    return getDefaultResponse();
  };

  const containsKeywords = (message, keywords) => {
    return keywords.some(keyword => message.includes(keyword));
  };

  const getServicesResponse = () => ({
    content: `Great! We offer comprehensive software development services:

🌐 **Web Development**
Custom web applications, e-commerce platforms, PWAs

📱 **Mobile Development**
iOS & Android apps, cross-platform solutions

☁️ **DevOps & Cloud**
Cloud migration, CI/CD pipelines, infrastructure automation

💼 **Consulting & Strategy**
Technical architecture, code audits, digital transformation

Which area interests you most?`,
    options: {
      quickActions: [
        { label: 'Web Development', value: 'web', icon: 'fas fa-globe' },
        { label: 'Mobile Apps', value: 'mobile', icon: 'fas fa-mobile-alt' },
        { label: 'DevOps & Cloud', value: 'devops', icon: 'fas fa-cloud' },
        { label: 'Get Quote', value: 'quote', icon: 'fas fa-calculator' }
      ]
    }
  });

  const getQuoteResponse = () => ({
    content: `I'd be happy to help you get a project quote! Let me gather some information:

**What type of project are you planning?**`,
    options: {
      quickActions: [
        { label: 'New Website', value: 'quote_web', icon: 'fas fa-globe' },
        { label: 'Mobile App', value: 'quote_mobile', icon: 'fas fa-mobile-alt' },
        { label: 'System Upgrade', value: 'quote_upgrade', icon: 'fas fa-sync' },
        { label: 'Not Sure', value: 'quote_unsure', icon: 'fas fa-question' }
      ]
    }
  });

  const getPortfolioResponse = () => ({
    content: `**Our Recent Work 🎯**

🏥 **HealthTech Platform**
Patient management system, HIPAA-compliant, 50K+ users

💰 **FinTech Dashboard**
Real-time trading platform, advanced analytics, 99.9% uptime

🛒 **E-commerce Marketplace**
Multi-vendor platform, $2M+ monthly transactions

Would you like to see detailed case studies?`,
    options: {
      quickActions: [
        { label: 'Case Studies', value: 'case_studies', icon: 'fas fa-file-alt' },
        { label: 'Live Demos', value: 'demos', icon: 'fas fa-play' },
        { label: 'Similar Project', value: 'similar', icon: 'fas fa-handshake' },
        { label: 'Get Quote', value: 'quote', icon: 'fas fa-calculator' }
      ]
    }
  });

  const getHumanHandoffResponse = () => ({
    content: `I'd be happy to connect you with one of our solution architects!

**Available Options:**

📞 **Immediate Call** - Available Mon-Fri, 9 AM - 6 PM PST
📅 **Schedule Consultation** - 45-minute detailed discussion
💬 **Continue via Email** - Get detailed information packet

What works best for you?`,
    options: {
      quickActions: [
        { label: 'Call Now', value: 'call_now', icon: 'fas fa-phone' },
        { label: 'Schedule Meeting', value: 'schedule', icon: 'fas fa-calendar' },
        { label: 'Email Info', value: 'email', icon: 'fas fa-envelope' },
        { label: 'Continue Chat', value: 'continue', icon: 'fas fa-comments' }
      ]
    }
  });

  const getDefaultResponse = () => ({
    content: `I want to make sure I understand you correctly.

I can help you with:
• Learning about our services
• Getting project quotes
• Viewing our portfolio
• Connecting with our team

What would you like to know more about?`,
    options: {
      suggestions: [
        'Tell me about your services',
        'I need a quote',
        'Show me your work',
        'Talk to a human'
      ]
    }
  });

  const handleQuickAction = (action) => {
    setShowSuggestions(false);

    switch (action) {
      case 'services':
        addMessage('Tell me about your services', 'user');
        setTimeout(() => processUserMessage('services'), 500);
        break;
      case 'quote':
        addMessage('I need a project quote', 'user');
        setTimeout(() => processUserMessage('quote'), 500);
        break;
      case 'portfolio':
        addMessage('Show me your portfolio', 'user');
        setTimeout(() => processUserMessage('portfolio'), 500);
        break;
      case 'human':
        addMessage('I want to talk to a human', 'user');
        setTimeout(() => processUserMessage('human'), 500);
        break;
      case 'schedule':
      case 'call_now':
      case 'email':
        setShowContactModal(true);
        break;
      default:
        addMessage(`I'd like to know more about ${action}`, 'user');
        setTimeout(() => processUserMessage(action), 500);
    }
  };

  const handleSuggestion = (suggestion) => {
    addMessage(suggestion, 'user');
    setShowSuggestions(false);
    setTimeout(() => processUserMessage(suggestion), 500);
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && inputValue.trim()) {
      handleSendMessage();
    }
  };

  const formatMessageContent = (content) => {
    return content.split('\n').map((line, index) => (
      <p key={index}>{line}</p>
    ));
  };

  return (
    <>
      {/* Toggle Button */}
      {!isOpen && (
        <div className="chatbot-toggle" onClick={() => setIsOpen(true)}>
          <i className="fas fa-comments"></i>
          <span className="notification-badge">1</span>
        </div>
      )}

      {/* Chatbot Container */}
      {isOpen && (
        <div className={`chatbot-container ${isMinimized ? 'minimized' : ''}`}>
          {/* Header */}
          <div className="chatbot-header">
            <div className="header-content">
              <div className="bot-avatar">
                <i className="fas fa-robot"></i>
              </div>
              <div className="bot-info">
                <h3>Alex</h3>
                <span className="status online">
                  <i className="fas fa-circle"></i>
                  Online
                </span>
              </div>
            </div>
            <div className="header-actions">
              <button 
                className="action-btn" 
                onClick={() => setIsMinimized(!isMinimized)}
                title="Minimize"
              >
                <i className="fas fa-minus"></i>
              </button>
              <button 
                className="action-btn" 
                onClick={() => setIsOpen(false)}
                title="Close"
              >
                <i className="fas fa-times"></i>
              </button>
            </div>
          </div>

          {!isMinimized && (
            <>
              {/* Messages */}
              <div className="messages-container">
                {messages.map((message) => (
                  <div key={message.id}>
                    <div className={`message ${message.sender}-message`}>
                      <div className="message-avatar">
                        <i className={`fas fa-${message.sender === 'bot' ? 'robot' : 'user'}`}></i>
                      </div>
                      <div className="message-content">
                        <div className="message-bubble">
                          {formatMessageContent(message.content)}
                        </div>
                        <div className="message-time">
                          {message.timestamp.toLocaleTimeString([], { 
                            hour: '2-digit', 
                            minute: '2-digit' 
                          })}
                        </div>
                      </div>
                    </div>

                    {/* Quick Actions */}
                    {message.quickActions && (
                      <div className="quick-actions">
                        {message.quickActions.map((action, index) => (
                          <button
                            key={index}
                            className="quick-action-btn"
                            onClick={() => handleQuickAction(action.value)}
                          >
                            <i className={action.icon}></i>
                            {action.label}
                          </button>
                        ))}
                      </div>
                    )}
                  </div>
                ))}

                {/* Typing Indicator */}
                {isTyping && (
                  <div className="typing-indicator">
                    <div className="message bot-message">
                      <div className="message-avatar">
                        <i className="fas fa-robot"></i>
                      </div>
                      <div className="message-content">
                        <div className="typing-dots">
                          <span></span>
                          <span></span>
                          <span></span>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                <div ref={messagesEndRef} />
              </div>

              {/* Input Area */}
              <div className="input-area">
                <div className="input-container">
                  <input
                    type="text"
                    value={inputValue}
                    onChange={(e) => setInputValue(e.target.value)}
                    onKeyPress={handleKeyPress}
                    placeholder="Type your message..."
                    autoComplete="off"
                  />
                  <button
                    className="send-btn"
                    onClick={handleSendMessage}
                    disabled={!inputValue.trim()}
                  >
                    <i className="fas fa-paper-plane"></i>
                  </button>
                </div>

                {/* Suggestions */}
                {showSuggestions && (
                  <div className="suggested-responses">
                    {suggestions.map((suggestion, index) => (
                      <button
                        key={index}
                        className="suggestion-btn"
                        onClick={() => handleSuggestion(suggestion)}
                      >
                        {suggestion}
                      </button>
                    ))}
                  </div>
                )}
              </div>

              {/* Footer */}
              <div className="chatbot-footer">
                <span>Powered by TechnoloWay AI</span>
              </div>
            </>
          )}
        </div>
      )}

      {/* Contact Modal */}
      {showContactModal && (
        <ContactModal 
          onClose={() => setShowContactModal(false)}
          onSubmit={(data) => {
            console.log('Contact data:', data);
            setShowContactModal(false);
            addMessage(
              `Thank you! I've received your contact information. Our team will reach out to you within 2 hours.`,
              'bot'
            );
          }}
        />
      )}
    </>
  );
};

// Contact Modal Component
const ContactModal = ({ onClose, onSubmit }) => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    company: '',
    contactMethod: 'email',
    contactTime: 'morning'
  });

  const handleSubmit = (e) => {
    e.preventDefault();
    onSubmit(formData);
  };

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  return (
    <div className="modal">
      <div className="modal-content">
        <div className="modal-header">
          <h3>Contact Information</h3>
          <button className="close-modal" onClick={onClose}>
            <i className="fas fa-times"></i>
          </button>
        </div>
        <form onSubmit={handleSubmit} className="contact-form">
          <div className="form-group">
            <label htmlFor="name">Name *</label>
            <input
              type="text"
              id="name"
              name="name"
              value={formData.name}
              onChange={handleChange}
              required
            />
          </div>
          <div className="form-group">
            <label htmlFor="email">Email *</label>
            <input
              type="email"
              id="email"
              name="email"
              value={formData.email}
              onChange={handleChange}
              required
            />
          </div>
          <div className="form-group">
            <label htmlFor="phone">Phone</label>
            <input
              type="tel"
              id="phone"
              name="phone"
              value={formData.phone}
              onChange={handleChange}
            />
          </div>
          <div className="form-group">
            <label htmlFor="company">Company</label>
            <input
              type="text"
              id="company"
              name="company"
              value={formData.company}
              onChange={handleChange}
            />
          </div>
          <div className="form-actions">
            <button type="button" className="btn-secondary" onClick={onClose}>
              Cancel
            </button>
            <button type="submit" className="btn-primary">
              Submit
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ChatbotComponent;
