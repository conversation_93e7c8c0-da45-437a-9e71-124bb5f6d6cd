/* Modern Homepage Styles - High Impact Design */

/* CSS Custom Properties - Design System */
:root {
    /* Brand Colors - Inspired by logo and client portal */
    --primary-color: #4f46e5;
    --primary-dark: #3730a3;
    --primary-light: #6366f1;
    --secondary-color: #6b7280;
    --accent-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;

    /* Neutral Colors */
    --white: #ffffff;
    --gray-50: #f8fafc;
    --gray-100: #f1f5f9;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e1;
    --gray-400: #94a3b8;
    --gray-500: #64748b;
    --gray-600: #475569;
    --gray-700: #334155;
    --gray-800: #1e293b;
    --gray-900: #0f172a;

    /* Typography */
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    --font-size-5xl: 3rem;
    --font-size-6xl: 3.75rem;

    /* Spacing */
    --space-1: 0.25rem;
    --space-2: 0.5rem;
    --space-3: 0.75rem;
    --space-4: 1rem;
    --space-5: 1.25rem;
    --space-6: 1.5rem;
    --space-8: 2rem;
    --space-10: 2.5rem;
    --space-12: 3rem;
    --space-16: 4rem;
    --space-20: 5rem;
    --space-24: 6rem;

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-2xl: 1.5rem;
    --radius-full: 9999px;

    /* Transitions */
    --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-normal: 300ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: 500ms cubic-bezier(0.4, 0, 0.2, 1);

    /* Z-index */
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal: 1040;
    --z-popover: 1050;
    --z-tooltip: 1060;
}

/* Global Styles */
* {
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: var(--font-family);
    line-height: 1.6;
    color: var(--gray-800);
    background-color: var(--white);
    margin: 0;
    padding: 0;
    overflow-x: hidden;
}

/* Enhanced Navigation */
.navbar-modern {
    background: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--gray-200);
    transition: var(--transition-normal);
    position: fixed;
    top: 0;
    width: 100%;
    z-index: var(--z-sticky);
}

.navbar-modern.scrolled {
    background: rgba(255, 255, 255, 0.98) !important;
    box-shadow: var(--shadow-lg);
}

.navbar-brand img {
    transition: var(--transition-normal);
}

.navbar-brand:hover img {
    transform: scale(1.05);
}

.navbar-nav .nav-link {
    font-weight: 500;
    color: var(--gray-700) !important;
    transition: var(--transition-fast);
    position: relative;
    margin: 0 var(--space-2);
}

.navbar-nav .nav-link::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 50%;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
    transition: var(--transition-normal);
    transform: translateX(-50%);
}

.navbar-nav .nav-link:hover::after,
.navbar-nav .nav-link.active::after {
    width: 100%;
}

.navbar-nav .nav-link:hover {
    color: var(--primary-color) !important;
}

/* Navbar Toggler for Modern Design */
.navbar-modern .navbar-toggler {
    border: none;
    padding: 0.25rem 0.5rem;
    background: transparent;
}

.navbar-modern .navbar-toggler:focus {
    box-shadow: none;
}

.navbar-modern .navbar-toggler-icon {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%2833, 37, 41, 0.75%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}

/* Hero Section - Modern Design */
.hero-modern {
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    background-color: #2c3e50;
    background-image: url('/images/Slide1.webp');
    background-size: contain;
    background-position: center;
    background-repeat: no-repeat;
    overflow: hidden;
    padding-top: 80px;
}

.hero-modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.4);
    z-index: 1;
}

.hero-content {
    position: relative;
    z-index: 2;
}

.hero-title {
    font-size: var(--font-size-5xl);
    font-weight: 800;
    line-height: 1.1;
    color: var(--white);
    margin-bottom: var(--space-6);
}

.hero-title .gradient-text {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: var(--font-size-xl);
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: var(--space-8);
    line-height: 1.7;
}

.hero-cta {
    display: flex;
    gap: var(--space-4);
    flex-wrap: wrap;
}

/* Modern Buttons */
.btn-modern {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: var(--space-4) var(--space-8);
    font-size: var(--font-size-base);
    font-weight: 600;
    border-radius: var(--radius-lg);
    text-decoration: none;
    transition: var(--transition-normal);
    position: relative;
    overflow: hidden;
    border: none;
    cursor: pointer;
    font-family: var(--font-family);
}

.btn-modern.btn-sm {
    padding: var(--space-3) var(--space-6);
    font-size: var(--font-size-sm);
}

.btn-primary-modern {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    color: var(--white);
    box-shadow: var(--shadow-lg);
}

.btn-primary-modern:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
    color: var(--white);
    text-decoration: none;
}

.btn-secondary-modern {
    background: var(--white);
    color: var(--gray-700);
    border: 2px solid var(--gray-300);
    box-shadow: var(--shadow-md);
}

.btn-secondary-modern:hover {
    background: var(--gray-50);
    border-color: var(--primary-color);
    color: var(--primary-color);
    transform: translateY(-2px);
    text-decoration: none;
}

/* Floating Animation - Removed as hero-image is no longer used */

/* Section Spacing */
.section-modern {
    padding: var(--space-24) 0;
    position: relative;
}

.section-header {
    text-align: center;
    margin-bottom: var(--space-16);
}

.section-title {
    font-size: var(--font-size-4xl);
    font-weight: 800;
    color: var(--gray-900);
    margin-bottom: var(--space-4);
}

.section-subtitle {
    font-size: var(--font-size-lg);
    color: var(--gray-600);
    max-width: 600px;
    margin: 0 auto;
}

/* Service Cards - Modern Design */
.service-card-modern {
    background: var(--white);
    border-radius: var(--radius-xl);
    padding: var(--space-8);
    box-shadow: var(--shadow-md);
    transition: var(--transition-normal);
    position: relative;
    overflow: hidden;
    height: 100%;
    border: 1px solid var(--gray-200);
}

.service-card-modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
    transform: scaleX(0);
    transition: var(--transition-normal);
}

.service-card-modern:hover::before {
    transform: scaleX(1);
}

.service-card-modern:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
}

.service-icon-modern {
    width: 64px;
    height: 64px;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: var(--space-6);
    transition: var(--transition-normal);
}

.service-icon-modern i {
    font-size: var(--font-size-2xl);
    color: var(--white);
}

.service-card-modern:hover .service-icon-modern {
    transform: scale(1.1) rotate(5deg);
}

.service-title {
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: var(--space-3);
}

.service-description {
    color: var(--gray-600);
    line-height: 1.7;
    margin-bottom: var(--space-6);
}

/* Animation Classes */
.fade-in-up {
    opacity: 0;
    transform: translateY(30px);
    transition: var(--transition-slow);
}

.fade-in-up.animate {
    opacity: 1;
    transform: translateY(0);
}

.fade-in-left {
    opacity: 0;
    transform: translateX(-30px);
    transition: var(--transition-slow);
}

.fade-in-left.animate {
    opacity: 1;
    transform: translateX(0);
}

.fade-in-right {
    opacity: 0;
    transform: translateX(30px);
    transition: var(--transition-slow);
}

.fade-in-right.animate {
    opacity: 1;
    transform: translateX(0);
}

.scale-in {
    opacity: 0;
    transform: scale(0.9);
    transition: var(--transition-slow);
}

.scale-in.animate {
    opacity: 1;
    transform: scale(1);
}

/* Stagger Animation Delays */
.animate-delay-100 { transition-delay: 100ms; }
.animate-delay-200 { transition-delay: 200ms; }
.animate-delay-300 { transition-delay: 300ms; }
.animate-delay-400 { transition-delay: 400ms; }
.animate-delay-500 { transition-delay: 500ms; }

/* Responsive Design */
@media (max-width: 768px) {
    .hero-title {
        font-size: var(--font-size-4xl);
    }

    .hero-subtitle {
        font-size: var(--font-size-lg);
    }

    .hero-cta {
        flex-direction: column;
        align-items: stretch;
    }

    .btn-modern {
        justify-content: center;
    }

    .section-title {
        font-size: var(--font-size-3xl);
    }

    .section-modern {
        padding: var(--space-16) 0;
    }
}

@media (max-width: 576px) {
    .hero-title {
        font-size: var(--font-size-3xl);
    }

    .service-card-modern {
        padding: var(--space-6);
    }
}

/* Project Cards - Modern Design */
.project-card-modern {
    position: relative;
    border-radius: var(--radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-lg);
    transition: var(--transition-normal);
    background: var(--white);
    height: 350px;
}

.project-card-modern:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-2xl);
}

.project-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
    transition: var(--transition-slow);
}

.project-card-modern:hover .project-image {
    transform: scale(1.1);
}

.project-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(79, 70, 229, 0.9), rgba(16, 185, 129, 0.9));
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    opacity: 0;
    transition: var(--transition-normal);
    color: var(--white);
    text-align: center;
    padding: var(--space-6);
}

.project-card-modern:hover .project-overlay {
    opacity: 1;
}

.project-content {
    padding: var(--space-6);
}

.project-title {
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: var(--space-2);
}

.project-description {
    color: var(--gray-600);
    font-size: var(--font-size-sm);
}

/* Technology Logos - Modern Carousel */
.tech-carousel {
    overflow: hidden;
    position: relative;
    background: var(--gray-50);
    padding: var(--space-12) 0;
}

.tech-track {
    display: flex;
    animation: scroll-infinite 30s linear infinite;
    gap: var(--space-8);
}

.tech-item {
    flex-shrink: 0;
    width: 120px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    transition: var(--transition-normal);
}

.tech-item:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-md);
}

.tech-logo {
    max-width: 80px;
    max-height: 50px;
    object-fit: contain;
    filter: grayscale(100%);
    transition: var(--transition-normal);
}

.tech-item:hover .tech-logo {
    filter: grayscale(0%);
}

@keyframes scroll-infinite {
    0% { transform: translateX(0); }
    100% { transform: translateX(-50%); }
}

/* Testimonial Cards - Modern Design */
.testimonial-card-modern {
    background: var(--white);
    border-radius: var(--radius-xl);
    padding: var(--space-8);
    box-shadow: var(--shadow-lg);
    transition: var(--transition-normal);
    position: relative;
    height: 100%;
}

.testimonial-card-modern::before {
    content: '"';
    position: absolute;
    top: var(--space-4);
    left: var(--space-6);
    font-size: 4rem;
    color: var(--primary-color);
    font-family: serif;
    opacity: 0.3;
}

.testimonial-card-modern:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
}

.testimonial-content {
    font-size: var(--font-size-lg);
    line-height: 1.8;
    color: var(--gray-700);
    margin-bottom: var(--space-6);
    font-style: italic;
}

.testimonial-author {
    display: flex;
    align-items: center;
    gap: var(--space-4);
}

.testimonial-avatar {
    width: 60px;
    height: 60px;
    border-radius: var(--radius-full);
    object-fit: cover;
    border: 3px solid var(--primary-color);
}

.testimonial-info h5 {
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: var(--space-1);
}

.testimonial-info p {
    color: var(--gray-600);
    font-size: var(--font-size-sm);
    margin: 0;
}

.testimonial-rating {
    display: flex;
    gap: var(--space-1);
    margin-bottom: var(--space-4);
}

.testimonial-rating i {
    color: #fbbf24;
}

/* CTA Section - Modern Design */
.cta-modern {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    color: var(--white);
    position: relative;
    overflow: hidden;
}

.cta-modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1.5" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="80" r="1" fill="rgba(255,255,255,0.1)"/></svg>');
    animation: float-particles 20s linear infinite;
}

@keyframes float-particles {
    0% { transform: translateY(0); }
    100% { transform: translateY(-100px); }
}

.cta-content {
    position: relative;
    z-index: 2;
    text-align: center;
}

.cta-title {
    font-size: var(--font-size-4xl);
    font-weight: 800;
    margin-bottom: var(--space-4);
}

.cta-subtitle {
    font-size: var(--font-size-xl);
    opacity: 0.9;
    margin-bottom: var(--space-8);
}

/* Utility Classes */
.text-gradient {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.bg-gradient {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
}

.glass-effect {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Loading Animation */
.loading-skeleton {
    background: linear-gradient(90deg, var(--gray-200) 25%, var(--gray-100) 50%, var(--gray-200) 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

/* Scroll Indicator */
.scroll-indicator {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: var(--gray-200);
    z-index: var(--z-sticky);
}

.scroll-progress {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
    width: 0%;
    transition: width 0.1s ease;
}

/* Intersection Observer Animations */
.observe-fade-up {
    opacity: 0;
    transform: translateY(50px);
    transition: opacity 0.8s ease, transform 0.8s ease;
}

.observe-fade-up.in-view {
    opacity: 1;
    transform: translateY(0);
}

.observe-fade-left {
    opacity: 0;
    transform: translateX(-50px);
    transition: opacity 0.8s ease, transform 0.8s ease;
}

.observe-fade-left.in-view {
    opacity: 1;
    transform: translateX(0);
}

.observe-fade-right {
    opacity: 0;
    transform: translateX(50px);
    transition: opacity 0.8s ease, transform 0.8s ease;
}

.observe-fade-right.in-view {
    opacity: 1;
    transform: translateX(0);
}

.observe-scale {
    opacity: 0;
    transform: scale(0.8);
    transition: opacity 0.8s ease, transform 0.8s ease;
}

.observe-scale.in-view {
    opacity: 1;
    transform: scale(1);
}

/* Performance Optimizations */
.will-change-transform {
    will-change: transform;
}

.will-change-opacity {
    will-change: opacity;
}

/* Contact Page Specific Styles */
.modern-page-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 60px 0 40px;
    position: relative;
    overflow: hidden;
    margin-top: 80px;
}

.page-header-content {
    position: relative;
    z-index: 2;
}

.page-breadcrumb {
    display: flex;
    align-items: center;
    margin-bottom: 0.75rem;
    font-size: 0.8rem;
}

.breadcrumb-link {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
}

.breadcrumb-link:hover {
    color: white;
    transform: translateX(2px);
}

.breadcrumb-separator {
    color: rgba(255, 255, 255, 0.6);
    margin: 0 1rem;
    font-size: 0.8rem;
}

.breadcrumb-current {
    color: white;
    font-weight: 500;
}

.page-title {
    font-size: 2.25rem;
    font-weight: 700;
    color: white;
    margin-bottom: 0.75rem;
    line-height: 1.2;
}

.title-highlight {
    background: linear-gradient(135deg, #fbbf24, #f59e0b);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.page-subtitle {
    font-size: 0.95rem;
    color: rgba(255, 255, 255, 0.9);
    line-height: 1.4;
    max-width: 450px;
}

.page-header-visual {
    position: relative;
    height: 150px;
}

.floating-element {
    position: absolute;
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.4rem;
    color: white;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    animation: float 6s ease-in-out infinite;
}

.floating-element i {
    width: 22px;
    height: 22px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
}

.contact-icon {
    top: 20%;
    right: 20%;
    animation-delay: 0s;
}

.phone-icon {
    top: 60%;
    right: 50%;
    animation-delay: 2s;
}

.location-icon {
    top: 40%;
    right: 10%;
    animation-delay: 4s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    33% { transform: translateY(-20px) rotate(5deg); }
    66% { transform: translateY(-10px) rotate(-5deg); }
}

.header-decoration {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
}

.decoration-circle {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.05);
    animation: pulse 4s ease-in-out infinite;
}

.circle-1 {
    width: 200px;
    height: 200px;
    top: 10%;
    left: 10%;
    animation-delay: 0s;
}

.circle-2 {
    width: 150px;
    height: 150px;
    top: 60%;
    left: 70%;
    animation-delay: 1s;
}

.circle-3 {
    width: 100px;
    height: 100px;
    top: 30%;
    left: 80%;
    animation-delay: 2s;
}

/* Modern Section Styles */
.modern-section {
    padding: 100px 0;
    position: relative;
}

.modern-card {
    background: white;
    border-radius: 24px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.card-header {
    padding: 2rem 2rem 1.25rem;
    border-bottom: 1px solid #f1f5f9;
}

.card-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 0.75rem;
}

.card-subtitle {
    color: #6b7280;
    font-size: 0.95rem;
    line-height: 1.5;
}

.card-body {
    padding: 2rem;
}

/* Contact Section Styles */
.contact-section {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.contact-info-section {
    padding-right: 2rem;
}

.contact-info-grid {
    margin: 2rem 0;
}

.contact-info-card {
    display: flex;
    align-items: center;
    padding: 1.25rem 1.5rem;
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    margin-bottom: 0.75rem;
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.contact-info-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12);
}

.contact-info-card:hover .contact-icon {
    transform: scale(1.1);
    color: #667eea;
}

.contact-icon {
    font-size: 1.5rem;
    color: #764ba2;
    margin-right: 1rem;
    flex-shrink: 0;
    transition: all 0.3s ease;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.contact-icon img {
    width: 24px;
    height: 24px;
    object-fit: contain;
}

.contact-info-content {
    flex: 1;
}

.contact-info-title {
    font-size: 1rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 0.25rem;
    line-height: 1.3;
}

.contact-info-value {
    color: #6b7280;
    font-size: 0.9rem;
    line-height: 1.4;
}

.contact-link {
    color: #667eea;
    text-decoration: none;
    transition: all 0.3s ease;
    position: relative;
}

.contact-link:hover {
    color: #764ba2;
    transform: translateX(2px);
}

.contact-info-placeholder {
    text-align: center;
    padding: 3rem 2rem;
    color: #9ca3af;
}

.placeholder-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

/* Social Section */
.social-section {
    margin-top: 3rem;
}

.social-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 1.5rem;
}

.social-links {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.social-link {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1.5rem;
    background: white;
    border-radius: 50px;
    text-decoration: none;
    color: #6b7280;
    border: 2px solid #e5e7eb;
    transition: all 0.3s ease;
    font-weight: 500;
    font-size: 0.9rem;
}

.social-link:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.social-link.facebook:hover {
    border-color: #1877f2;
    color: #1877f2;
}

.social-link.twitter:hover {
    border-color: #1da1f2;
    color: #1da1f2;
}

.social-link.linkedin:hover {
    border-color: #0077b5;
    color: #0077b5;
}

.social-link.instagram:hover {
    border-color: #e4405f;
    color: #e4405f;
}

/* Contact Form Styles */
.contact-form-section {
    padding-left: 2rem;
}

.modern-form {
    margin-top: 2rem;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}

.form-group {
    position: relative;
    margin-bottom: 1.25rem;
}

.form-group.full-width {
    grid-column: 1 / -1;
    margin-bottom: 1.25rem;
}

.form-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.form-label i {
    color: #667eea;
    width: 14px;
    height: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    font-size: 0.85rem;
    flex-shrink: 0;
}

.form-input,
.form-textarea {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 2px solid #e5e7eb;
    border-radius: 10px;
    font-size: 0.95rem;
    transition: all 0.3s ease;
    background: #fafafa;
    font-family: inherit;
}

.form-input:focus,
.form-textarea:focus {
    outline: none;
    border-color: #667eea;
    background: white;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-group.focused .form-label {
    color: #667eea;
}

.form-error {
    color: #ef4444;
    font-size: 0.875rem;
    margin-top: 0.5rem;
    display: block;
}

.form-actions {
    margin-top: 2rem;
    text-align: right;
}

.modern-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 10px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    cursor: pointer;
    font-family: inherit;
    position: relative;
    overflow: hidden;
    font-size: 0.95rem;
}

.modern-btn.primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    box-shadow: 0 8px 16px rgba(102, 126, 234, 0.25);
}

.modern-btn.primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 12px 24px rgba(102, 126, 234, 0.35);
}

.modern-btn.secondary {
    background: white;
    color: #667eea;
    border: 2px solid #667eea;
}

.modern-btn.secondary:hover {
    background: #667eea;
    color: white;
    transform: translateY(-1px);
}

.modern-btn.large {
    padding: 1rem 2rem;
    font-size: 1rem;
}

.modern-btn.small {
    padding: 0.5rem 1rem;
    font-size: 0.85rem;
}

.btn-text {
    position: relative;
    z-index: 2;
}

.btn-icon {
    position: relative;
    z-index: 2;
    transition: transform 0.3s ease;
}

.modern-btn:hover .btn-icon {
    transform: translateX(3px);
}

/* Modern Alerts */
.modern-alert {
    display: flex;
    align-items: flex-start;
    padding: 1.5rem;
    border-radius: 12px;
    margin-bottom: 2rem;
    position: relative;
}

.modern-alert.success {
    background: linear-gradient(135deg, #d1fae5, #a7f3d0);
    border: 1px solid #10b981;
}

.modern-alert.error {
    background: linear-gradient(135deg, #fee2e2, #fecaca);
    border: 1px solid #ef4444;
}

.alert-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    font-size: 1.5rem;
}

.modern-alert.success .alert-icon {
    background: #10b981;
    color: white;
}

.modern-alert.error .alert-icon {
    background: #ef4444;
    color: white;
}

.alert-content h4 {
    margin: 0 0 0.5rem 0;
    font-weight: 600;
}

.alert-content p {
    margin: 0;
    opacity: 0.8;
}

.alert-close {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: none;
    border: none;
    font-size: 1.2rem;
    cursor: pointer;
    opacity: 0.6;
    transition: opacity 0.3s ease;
}

.alert-close:hover {
    opacity: 1;
}

/* Map Section */
.map-section {
    background: #f8fafc;
    padding: 100px 0;
}

.map-container {
    margin-top: 3rem;
}

.map-wrapper {
    position: relative;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.modern-map {
    width: 100%;
    height: 400px;
    border: none;
    border-radius: 20px;
    min-height: 400px;
    display: block;
    background: #f8fafc;
}

.map-fallback-container {
    width: 100%;
    height: 400px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-radius: 20px;
    padding: 2rem;
    border: 1px solid #e2e8f0;
}

.fallback-content {
    text-align: center;
    max-width: 400px;
}

.fallback-icon {
    font-size: 4rem;
    color: #667eea;
    margin-bottom: 1.5rem;
    animation: pulse 2s infinite;
}

.fallback-content h4 {
    color: #1f2937;
    margin-bottom: 0.5rem;
    font-size: 1.5rem;
    font-weight: 600;
}

.fallback-content > p {
    color: #6b7280;
    margin-bottom: 2rem;
    font-size: 1.1rem;
}

.fallback-address {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    margin: 1.5rem 0;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    text-align: left;
}

.fallback-address p {
    margin: 0.75rem 0;
    color: #374151;
    font-size: 0.95rem;
    display: flex;
    align-items: center;
}

.fallback-address p:first-child {
    margin-top: 0;
}

.fallback-address p:last-child {
    margin-bottom: 0;
}

.fallback-address i {
    color: #667eea;
    width: 16px;
    margin-right: 0.75rem;
    flex-shrink: 0;
}

.fallback-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

.fallback-actions .modern-btn {
    min-width: 140px;
}

.map-overlay {
    position: absolute;
    top: 2rem;
    left: 2rem;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 1.5rem;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.map-info h4 {
    margin: 0 0 0.5rem 0;
    font-weight: 600;
    color: #1f2937;
}

.map-info p {
    margin: 0 0 1rem 0;
    color: #6b7280;
    font-size: 0.9rem;
}

/* Animation Classes */
.animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out forwards;
}

.animate-in {
    animation: slideInUp 0.8s ease-out forwards;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.floating-animation {
    animation: float 6s ease-in-out infinite;
}

.ripple-effect {
    position: absolute;
    border-radius: 50%;
    background: rgba(102, 126, 234, 0.3);
    transform: scale(0);
    animation: ripple 0.6s linear;
    pointer-events: none;
}

@keyframes ripple {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .tech-track {
        animation: none;
    }
}

/* Admin Login Page Styles */
.modern-login-body {
    font-family: var(--font-family);
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    position: relative;
    overflow-x: hidden;
}

.login-background {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
    pointer-events: none;
}

.login-decoration {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

.floating-elements {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

.floating-element.admin-icon {
    top: 15%;
    right: 15%;
    animation-delay: 0s;
}

.floating-element.security-icon {
    top: 60%;
    left: 10%;
    animation-delay: 2s;
}

.floating-element.dashboard-icon {
    top: 30%;
    right: 5%;
    animation-delay: 4s;
}

.login-main {
    position: relative;
    z-index: 2;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Modern Login Header */
.modern-login-header {
    padding: 80px 0 40px;
    position: relative;
    z-index: 2;
}

.login-header-content {
    color: white;
}

.login-breadcrumb {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1rem;
    font-size: 0.85rem;
}

.login-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: white;
    margin-bottom: 1rem;
    line-height: 1.2;
}

.login-subtitle {
    font-size: 1rem;
    color: rgba(255, 255, 255, 0.9);
    line-height: 1.5;
    max-width: 500px;
    margin: 0 auto;
}

/* Login Section */
.login-section {
    flex: 1;
    display: flex;
    align-items: center;
    padding: 40px 0 60px;
}

.login-card {
    max-width: 420px;
    margin: 0 auto;
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.12);
}

.login-logo {
    margin-bottom: 1.5rem;
}

.brand-logo {
    max-height: 60px;
    width: auto;
}

/* Password Input Wrapper */
.password-input-wrapper {
    position: relative;
}

.password-toggle {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #6b7280;
    cursor: pointer;
    padding: 0.5rem;
    transition: color 0.3s ease;
}

.password-toggle:hover {
    color: #667eea;
}

/* Modern Checkbox */
.form-check-wrapper {
    margin: 1rem 0;
}

.modern-checkbox {
    display: flex;
    align-items: center;
    cursor: pointer;
    user-select: none;
    position: relative;
}

.modern-checkbox input[type="checkbox"] {
    position: absolute;
    opacity: 0;
    cursor: pointer;
}

.checkmark {
    height: 16px;
    width: 16px;
    background-color: #f8fafc;
    border: 2px solid #e5e7eb;
    border-radius: 3px;
    margin-right: 0.5rem;
    position: relative;
    transition: all 0.3s ease;
}

.modern-checkbox:hover .checkmark {
    border-color: #667eea;
}

.modern-checkbox input:checked ~ .checkmark {
    background-color: #667eea;
    border-color: #667eea;
}

.checkmark:after {
    content: "";
    position: absolute;
    display: none;
    left: 4px;
    top: 1px;
    width: 4px;
    height: 8px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

.modern-checkbox input:checked ~ .checkmark:after {
    display: block;
}

.checkbox-text {
    color: #374151;
    font-size: 0.9rem;
    font-weight: 500;
}

/* Full Width Button */
.modern-btn.full-width {
    width: 100%;
    justify-content: center;
}

/* Login Footer */
.login-footer {
    margin-top: 1.5rem;
    padding-top: 1.5rem;
    border-top: 1px solid #f1f5f9;
}

.footer-links {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 0.75rem;
}

.footer-link {
    display: flex;
    align-items: center;
    gap: 0.4rem;
    color: #6b7280;
    text-decoration: none;
    font-size: 0.85rem;
    transition: color 0.3s ease;
}

.footer-link:hover {
    color: #667eea;
}

.footer-link i {
    font-size: 0.75rem;
}

/* Security Notice */
.security-notice {
    margin-top: 1.5rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
}

.security-icon {
    width: 32px;
    height: 32px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1rem;
    flex-shrink: 0;
}

.security-content {
    color: white;
}

.security-content h5 {
    margin: 0 0 0.4rem 0;
    font-weight: 600;
    font-size: 0.9rem;
}

.security-content p {
    margin: 0;
    font-size: 0.8rem;
    opacity: 0.9;
    line-height: 1.4;
}

/* Loading State */
.modern-btn.loading {
    pointer-events: none;
    opacity: 0.8;
}

/* Validation Summary */
.validation-summary {
    background: linear-gradient(135deg, #fee2e2, #fecaca);
    border: 1px solid #ef4444;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1.5rem;
    color: #dc2626;
    font-size: 0.9rem;
}

.validation-summary ul {
    margin: 0;
    padding-left: 1.2rem;
}

/* Contact Page Responsive Design */
@media (max-width: 768px) {
    .page-title {
        font-size: 1.75rem;
    }

    .page-subtitle {
        font-size: 0.9rem;
    }

    .contact-info-section,
    .contact-form-section {
        padding: 0;
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .floating-element {
        width: 60px;
        height: 60px;
        font-size: 1.4rem;
    }

    .floating-element i {
        width: 22px;
        height: 22px;
    }

    .page-header-visual {
        height: 200px;
    }

    .social-links {
        justify-content: center;
    }

    .map-overlay {
        position: relative;
        top: auto;
        left: auto;
        margin: 1rem;
    }

    /* Login Page Mobile */
    .login-title {
        font-size: 2rem;
    }

    .login-subtitle {
        font-size: 0.95rem;
    }

    .modern-login-header {
        padding: 80px 0 30px;
    }

    .login-section {
        padding: 30px 0 50px;
    }

    .footer-links {
        flex-direction: column;
        text-align: center;
        gap: 0.5rem;
    }

    .security-notice {
        flex-direction: column;
        text-align: center;
        padding: 0.75rem;
    }
}

@media (max-width: 480px) {
    .page-title {
        font-size: 1.5rem;
    }

    .floating-element {
        width: 50px;
        height: 50px;
        font-size: 1.1rem;
    }

    .floating-element i {
        width: 18px;
        height: 18px;
    }

    .contact-info-card {
        padding: 1rem 1.25rem;
    }

    .contact-icon {
        font-size: 1.25rem;
        width: 20px;
        height: 20px;
        margin-right: 0.75rem;
    }

    .contact-icon img {
        width: 20px;
        height: 20px;
    }

    /* Login Page Mobile */
    .login-title {
        font-size: 1.75rem;
    }

    .brand-logo {
        max-height: 50px;
    }

    .login-card {
        margin: 0 0.75rem;
    }

    .card-header {
        padding: 1.5rem 1.5rem 1rem;
    }

    .card-body {
        padding: 1.5rem;
    }
}

/* Client Login Page Styles */
.modern-client-login-body {
    font-family: var(--font-family);
    background: linear-gradient(135deg, #4f46e5 0%, #3730a3 100%);
    min-height: 100vh;
    position: relative;
    overflow-x: hidden;
}

.client-login-background {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
    pointer-events: none;
}

.floating-element.client-icon {
    top: 15%;
    right: 15%;
    animation-delay: 0s;
}

.floating-element.project-icon {
    top: 60%;
    left: 10%;
    animation-delay: 2s;
}

.floating-element.portal-icon {
    top: 30%;
    right: 5%;
    animation-delay: 4s;
}

/* Modern Client Login Header */
.modern-client-login-header {
    padding: 80px 0 40px;
    position: relative;
    z-index: 2;
}

/* Client Login Section */
.client-login-section {
    flex: 1;
    display: flex;
    align-items: center;
    padding: 40px 0 60px;
}

/* Client Primary Button */
.modern-btn.client-primary {
    background: linear-gradient(135deg, #4f46e5, #3730a3);
    color: white;
    box-shadow: 0 8px 16px rgba(79, 70, 229, 0.25);
}

.modern-btn.client-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 12px 24px rgba(79, 70, 229, 0.35);
}

/* Additional Links */
.additional-links {
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid #f1f5f9;
    text-align: center;
}

.additional-link {
    display: inline-flex;
    align-items: center;
    gap: 0.4rem;
    color: #6b7280;
    text-decoration: none;
    font-size: 0.8rem;
    margin: 0 0.75rem;
    transition: color 0.3s ease;
}

.additional-link:hover {
    color: #4f46e5;
}

.additional-link i {
    font-size: 0.7rem;
}

/* Client Portal Notice */
.client-portal-notice {
    margin-top: 1.5rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
}

.portal-icon {
    width: 32px;
    height: 32px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1rem;
    flex-shrink: 0;
}

.portal-content {
    color: white;
}

.portal-content h5 {
    margin: 0 0 0.4rem 0;
    font-weight: 600;
    font-size: 0.9rem;
}

.portal-content p {
    margin: 0;
    font-size: 0.8rem;
    opacity: 0.9;
    line-height: 1.4;
}

/* Client Login Mobile Responsive */
@media (max-width: 768px) {
    .additional-links {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
        align-items: center;
    }

    .additional-link {
        margin: 0;
    }

    .client-portal-notice {
        flex-direction: column;
        text-align: center;
        padding: 0.75rem;
    }
}

@media (max-width: 480px) {
    .additional-link {
        font-size: 0.75rem;
    }

    .portal-content h5 {
        font-size: 0.85rem;
    }

    .portal-content p {
        font-size: 0.75rem;
    }
}

/* Technologies Page Specific Styles */
.tech-icon {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    animation: float 3s ease-in-out infinite;
}

.tech-icon-2 {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    animation: float 3s ease-in-out infinite 1s;
    top: 60%;
    right: 20%;
}

.tech-icon-3 {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    animation: float 3s ease-in-out infinite 2s;
    top: 30%;
    right: 60%;
}

.tech-icon-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 80px;
    height: 80px;
    margin: 0 auto;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-radius: 20px;
    transition: all 0.3s ease;
}

.tech-icon-img {
    width: 50px;
    height: 50px;
    object-fit: contain;
    transition: all 0.3s ease;
}

.modern-card:hover .tech-icon-wrapper {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    transform: scale(1.1);
}

.modern-card:hover .tech-icon-img {
    filter: brightness(0) invert(1);
}

.tech-stack-card {
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.tech-stack-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
}

.tech-category-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    transition: all 0.3s ease;
}

.tech-category-icon i {
    font-size: 1.5rem;
    color: white;
}

.tech-stack-card:hover .tech-category-icon {
    transform: scale(1.1) rotate(5deg);
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.tech-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.tech-list li {
    padding: 0.5rem 0;
    color: #6b7280;
    font-size: 0.9rem;
    border-bottom: 1px solid #f1f5f9;
    transition: all 0.3s ease;
}

.tech-list li:last-child {
    border-bottom: none;
}

.tech-list li:hover {
    color: #667eea;
    padding-left: 0.5rem;
}

.process-card {
    background: white;
    transition: all 0.3s ease;
}

.process-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(255, 255, 255, 0.1);
}

.process-number {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    transition: all 0.3s ease;
}

.process-number span {
    font-size: 1.5rem;
    font-weight: 700;
    color: white;
}

.process-card:hover .process-number {
    transform: scale(1.1);
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.cta-card {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border: 1px solid rgba(0, 0, 0, 0.05);
    padding: 2rem;
}

/* Technologies Page Mobile Responsive */
@media (max-width: 768px) {
    .tech-icon-wrapper {
        width: 60px;
        height: 60px;
    }

    .tech-icon-img {
        width: 40px;
        height: 40px;
    }

    .tech-category-icon {
        width: 50px;
        height: 50px;
    }

    .tech-category-icon i {
        font-size: 1.2rem;
    }

    .process-number {
        width: 50px;
        height: 50px;
    }

    .process-number span {
        font-size: 1.2rem;
    }

    .cta-card {
        padding: 1.5rem;
    }
}

@media (max-width: 480px) {
    .tech-icon-wrapper {
        width: 50px;
        height: 50px;
    }

    .tech-icon-img {
        width: 30px;
        height: 30px;
    }

    .tech-category-icon {
        width: 40px;
        height: 40px;
    }

    .tech-category-icon i {
        font-size: 1rem;
    }

    .process-number {
        width: 40px;
        height: 40px;
    }

    .process-number span {
        font-size: 1rem;
    }

    .cta-card {
        padding: 1rem;
    }
}

/* Careers Page Specific Styles */
.career-icon {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    animation: float 3s ease-in-out infinite;
}

.career-icon-2 {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    animation: float 3s ease-in-out infinite 1s;
    top: 60%;
    right: 20%;
}

.career-icon-3 {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    animation: float 3s ease-in-out infinite 2s;
    top: 30%;
    right: 60%;
}

.job-card {
    transition: all 0.3s ease;
    margin-bottom: 1.5rem;
}

.job-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
}

.job-badges {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
}

.job-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.5rem 1rem;
    border-radius: 50px;
    font-size: 0.75rem;
    font-weight: 500;
    color: white;
}

.job-badge.employment-type {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.job-badge.location {
    background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
}

.job-badge.remote {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.job-badge.salary {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
}

.empty-state-card {
    padding: 3rem 2rem;
    text-align: center;
}

.empty-state-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 2rem;
    color: #667eea;
}

.benefits-card .card-body {
    padding-top: 0;
}

.benefits-list {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.benefit-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
}

.benefit-icon {
    width: 50px;
    height: 50px;
    min-width: 50px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
    transition: all 0.3s ease;
}

.benefit-content {
    flex: 1;
}

.benefit-title {
    font-size: 1rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 0.5rem;
}

.benefit-description {
    font-size: 0.9rem;
    color: #6b7280;
    margin-bottom: 0;
    line-height: 1.5;
}

.contact-hr-card .card-body {
    padding-top: 0;
}

/* Careers Page Mobile Responsive */
@media (max-width: 768px) {
    .job-badge {
        padding: 0.4rem 0.8rem;
        font-size: 0.7rem;
    }

    .empty-state-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }

    .benefit-icon {
        width: 40px;
        height: 40px;
        min-width: 40px;
        font-size: 1rem;
    }

    .benefit-title {
        font-size: 0.95rem;
    }

    .benefit-description {
        font-size: 0.85rem;
    }
}

@media (max-width: 480px) {
    .job-badges {
        gap: 0.3rem;
    }

    .job-badge {
        padding: 0.3rem 0.6rem;
        font-size: 0.65rem;
    }

    .empty-state-icon {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }

    .benefit-item {
        gap: 0.75rem;
    }

    .benefit-icon {
        width: 35px;
        height: 35px;
        min-width: 35px;
        font-size: 0.9rem;
    }
}

/* Blog Page Specific Styles */
.blog-icon {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    animation: float 3s ease-in-out infinite;
}

.blog-icon-2 {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    animation: float 3s ease-in-out infinite 1s;
    top: 60%;
    right: 20%;
}

.blog-icon-3 {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    animation: float 3s ease-in-out infinite 2s;
    top: 30%;
    right: 60%;
}

.blog-post-card {
    transition: all 0.3s ease;
    margin-bottom: 1.5rem;
    height: 100%;
}

.blog-post-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
}

.blog-image-wrapper {
    position: relative;
    height: 200px;
    overflow: hidden;
    border-radius: 16px 16px 0 0;
}

.blog-featured-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.blog-category-badge {
    position: absolute;
    top: 15px;
    left: 15px;
    z-index: 2;
}

.category-tag {
    display: inline-block;
    padding: 6px 12px;
    border-radius: 50px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    font-size: 0.75rem;
    font-weight: 500;
}

.blog-title-link {
    color: #1f2937;
    text-decoration: none;
    transition: color 0.3s ease;
}

.blog-title-link:hover {
    color: #667eea;
}

.blog-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.8rem;
    color: #6b7280;
}

.blog-author, .blog-date {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.blog-author i, .blog-date i {
    color: #667eea;
}

/* Modern Pagination */
.modern-pagination {
    margin-top: 3rem;
}

.pagination-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 1rem;
}

.pagination-numbers {
    display: flex;
    gap: 0.5rem;
}

.pagination-number {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: white;
    color: #1f2937;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.pagination-number:hover {
    background: #f1f5f9;
    transform: translateY(-2px);
}

.pagination-number.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.pagination-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border-radius: 50px;
    background: white;
    color: #1f2937;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.pagination-btn:hover {
    background: #f1f5f9;
    transform: translateY(-2px);
}

.pagination-btn i {
    transition: transform 0.3s ease;
}

.pagination-btn.prev-btn:hover i {
    transform: translateX(-3px);
}

.pagination-btn.next-btn:hover i {
    transform: translateX(3px);
}

/* Blog Sidebar Styles */
.search-card .card-body {
    padding-top: 0;
}

.search-input-wrapper {
    position: relative;
    transition: all 0.3s ease;
}

.search-input {
    width: 100%;
    padding: 12px 20px;
    border-radius: 50px;
    border: 2px solid #e2e8f0;
    font-size: 0.9rem;
    padding-right: 50px;
    transition: all 0.3s ease;
}

.search-input:focus {
    outline: none;
    border-color: #667eea;
}

.search-btn {
    position: absolute;
    right: 5px;
    top: 50%;
    transform: translateY(-50%);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.search-btn:hover {
    transform: translateY(-50%) scale(1.05);
}

.categories-list {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.category-link {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 1rem;
    border-radius: 8px;
    background: #f8fafc;
    color: #1f2937;
    text-decoration: none;
    transition: all 0.3s ease;
}

.category-link:hover {
    background: #f1f5f9;
    transform: translateX(5px);
}

.category-link.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.category-link i {
    transition: transform 0.3s ease;
}

.newsletter-input-wrapper {
    position: relative;
}

.newsletter-input {
    width: 100%;
    padding: 12px 20px;
    border-radius: 50px;
    border: 2px solid #e2e8f0;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.newsletter-input:focus {
    outline: none;
    border-color: #667eea;
}

/* Blog Page Mobile Responsive */
@media (max-width: 768px) {
    .blog-image-wrapper {
        height: 180px;
    }

    .category-tag {
        padding: 4px 10px;
        font-size: 0.7rem;
    }

    .blog-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .pagination-number {
        width: 35px;
        height: 35px;
    }

    .pagination-btn {
        padding: 0.4rem 0.8rem;
        font-size: 0.9rem;
    }

    .search-input, .newsletter-input {
        padding: 10px 15px;
    }

    .search-btn {
        width: 35px;
        height: 35px;
    }

    .category-link {
        padding: 0.6rem 0.8rem;
    }
}

@media (max-width: 480px) {
    .blog-image-wrapper {
        height: 160px;
    }

    .category-tag {
        padding: 3px 8px;
        font-size: 0.65rem;
    }

    .pagination-wrapper {
        gap: 0.5rem;
    }

    .pagination-number {
        width: 30px;
        height: 30px;
        font-size: 0.8rem;
    }

    .pagination-btn {
        padding: 0.3rem 0.6rem;
        font-size: 0.8rem;
    }

    .search-input, .newsletter-input {
        padding: 8px 12px;
        font-size: 0.8rem;
    }

    .search-btn {
        width: 30px;
        height: 30px;
        font-size: 0.8rem;
    }

    .category-link {
        padding: 0.5rem 0.7rem;
        font-size: 0.85rem;
    }
}

/* Projects Page Specific Styles */
.portfolio-icon {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    animation: float 3s ease-in-out infinite;
}

.portfolio-icon-2 {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    animation: float 3s ease-in-out infinite 1s;
    top: 60%;
    right: 20%;
}

.portfolio-icon-3 {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    animation: float 3s ease-in-out infinite 2s;
    top: 30%;
    right: 60%;
}

.filter-section {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    padding: 3rem 0;
}

.filter-card {
    background: white;
    border: 1px solid rgba(0, 0, 0, 0.05);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.08);
}

.filter-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 1rem;
}

.filter-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
}

.filter-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.25rem;
    border-radius: 50px;
    background: #f8fafc;
    color: #1f2937;
    text-decoration: none;
    font-weight: 500;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.filter-btn:hover {
    background: #f1f5f9;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.filter-btn.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-color: #667eea;
}

.filter-btn i {
    font-size: 0.9rem;
}

.project-card {
    transition: all 0.3s ease;
    margin-bottom: 1.5rem;
    height: 100%;
    overflow: hidden;
}

.project-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
}

.project-image-wrapper {
    position: relative;
    height: 250px;
    overflow: hidden;
    border-radius: 16px 16px 0 0;
}

.project-featured-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.project-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.9) 0%, rgba(118, 75, 162, 0.9) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.project-overlay-content {
    text-align: center;
}

.project-service-badge {
    position: absolute;
    top: 15px;
    left: 15px;
    z-index: 2;
}

.service-tag {
    display: inline-block;
    padding: 6px 12px;
    border-radius: 50px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    font-size: 0.75rem;
    font-weight: 500;
}

.project-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: auto;
}

.project-date {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.85rem;
    color: #6b7280;
}

.project-date i {
    color: #667eea;
}

.project-link {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #667eea;
    text-decoration: none;
    font-weight: 500;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.project-link:hover {
    color: #5a67d8;
}

.project-link i {
    transition: transform 0.3s ease;
}

/* Projects Page Mobile Responsive */
@media (max-width: 768px) {
    .filter-section {
        padding: 2rem 0;
    }

    .filter-buttons {
        gap: 0.5rem;
    }

    .filter-btn {
        padding: 0.6rem 1rem;
        font-size: 0.85rem;
    }

    .filter-title {
        font-size: 1rem;
    }

    .project-image-wrapper {
        height: 200px;
    }

    .service-tag {
        padding: 4px 10px;
        font-size: 0.7rem;
    }

    .project-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.75rem;
    }

    .project-date {
        font-size: 0.8rem;
    }

    .project-link {
        font-size: 0.85rem;
    }
}

@media (max-width: 480px) {
    .filter-card .row {
        flex-direction: column;
    }

    .filter-card .col-md-8,
    .filter-card .col-md-4 {
        margin-bottom: 1rem;
    }

    .filter-buttons {
        gap: 0.4rem;
    }

    .filter-btn {
        padding: 0.5rem 0.8rem;
        font-size: 0.8rem;
    }

    .project-image-wrapper {
        height: 180px;
    }

    .service-tag {
        padding: 3px 8px;
        font-size: 0.65rem;
    }

    .project-date {
        font-size: 0.75rem;
    }

    .project-link {
        font-size: 0.8rem;
    }
}

/* Services Page Specific Styles */
.services-icon {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    animation: float 3s ease-in-out infinite;
}

.services-icon-2 {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    animation: float 3s ease-in-out infinite 1s;
    top: 60%;
    right: 20%;
}

.services-icon-3 {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    animation: float 3s ease-in-out infinite 2s;
    top: 30%;
    right: 60%;
}

.services-grid-section {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    padding: 4rem 0;
}

.service-card-enhanced {
    height: 100%;
    position: relative;
    overflow: hidden;
}

.service-card-enhanced:hover {
    transition: none !important;
    transform: none !important;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
}

.service-card-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea, #764ba2);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.service-card-enhanced:hover::before {
    transform: scaleX(1);
}

.service-icon-wrapper {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    transition: all 0.3s ease;
}

.service-icon {
    font-size: 2rem;
    color: white;
}

.service-icon-img {
    width: 40px;
    height: 40px;
    object-fit: contain;
    filter: brightness(0) invert(1);
}

.service-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: auto;
    padding-top: 1rem;
    border-top: 1px solid #f1f5f9;
}

.service-price {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.price-label {
    font-size: 0.8rem;
    color: #6b7280;
    margin-bottom: 0.25rem;
}

.price-value {
    font-size: 1.25rem;
    font-weight: 700;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.feature-card {
    transition: all 0.3s ease;
    height: 100%;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.feature-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    transition: all 0.3s ease;
}

.feature-icon i {
    font-size: 1.5rem;
    color: white;
}

.feature-card:hover .feature-icon {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

/* Services Page Mobile Responsive */
@media (max-width: 768px) {
    .services-grid-section {
        padding: 3rem 0;
    }

    .service-icon-wrapper {
        width: 60px;
        height: 60px;
    }

    .service-icon {
        font-size: 1.5rem;
    }

    .service-icon-img {
        width: 30px;
        height: 30px;
    }

    .service-footer {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .price-value {
        font-size: 1.1rem;
    }

    .feature-icon {
        width: 50px;
        height: 50px;
    }

    .feature-icon i {
        font-size: 1.2rem;
    }
}

@media (max-width: 480px) {
    .services-grid-section {
        padding: 2rem 0;
    }

    .service-icon-wrapper {
        width: 50px;
        height: 50px;
    }

    .service-icon {
        font-size: 1.2rem;
    }

    .service-icon-img {
        width: 25px;
        height: 25px;
    }

    .price-value {
        font-size: 1rem;
    }

    .feature-icon {
        width: 40px;
        height: 40px;
    }

    .feature-icon i {
        font-size: 1rem;
    }
}

/* Enhanced Services Mobile Responsive Optimizations */
@media (max-width: 768px) {
    .service-card-enhanced:hover {
        transform: none;
        box-shadow:
            0 30px 60px rgba(102, 126, 234, 0.25),
            0 15px 30px rgba(118, 75, 162, 0.15),
            0 8px 20px rgba(0, 0, 0, 0.1);
        animation: none;
    }

    .service-card-enhanced:hover .service-featured-icon {
        transform: scale(1.1);
        filter: drop-shadow(0 0 15px rgba(102, 126, 234, 0.4));
    }

    .service-featured-icon {
        font-size: 4rem;
    }

    /* Reduce complex effects on mobile for performance */
    .service-card-enhanced .floating-elements {
        display: none;
    }

    .service-card-enhanced::before {
        animation: none;
    }

    /* Simplified mobile animations */
    .service-card-enhanced:hover .service-icon-display::before {
        width: 180px;
        height: 180px;
    }

    .service-card-enhanced:hover .card-title::before {
        display: none;
    }
}

@media (max-width: 480px) {
    .service-card-enhanced:hover {
        transform: none;
        box-shadow:
            0 25px 50px rgba(102, 126, 234, 0.2),
            0 12px 25px rgba(118, 75, 162, 0.12),
            0 6px 15px rgba(0, 0, 0, 0.08);
    }

    .service-card-enhanced:hover .service-featured-icon {
        transform: scale(1.05);
        filter: drop-shadow(0 0 10px rgba(102, 126, 234, 0.3));
    }

    .service-featured-icon {
        font-size: 3.5rem;
    }

    /* Further reduce effects on very small screens */
    .service-card-enhanced .card-title::after {
        display: none;
    }

    .service-card-enhanced:hover .service-icon-display::before {
        width: 150px;
        height: 150px;
    }
}

/* About Page Specific Styles */
.about-icon {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    animation: float 3s ease-in-out infinite;
}

.about-icon-2 {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    animation: float 3s ease-in-out infinite 1s;
    top: 60%;
    right: 20%;
}

.about-icon-3 {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    animation: float 3s ease-in-out infinite 2s;
    top: 30%;
    right: 60%;
}

.about-image-wrapper {
    position: relative;
    transition: all 0.3s ease;
}

.about-image-container {
    position: relative;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.about-featured-image {
    width: 100%;
    height: 400px;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.about-image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(16, 185, 129, 0.1));
}

.about-stats {
    position: absolute;
    bottom: 20px;
    left: 20px;
    right: 20px;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 0.75rem;
    opacity: 0.9;
    transform: translateY(10px);
    transition: all 0.3s ease;
}

.stat-item {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 0.75rem;
    border-radius: 12px;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
}

.stat-item:hover {
    background: rgba(255, 255, 255, 1);
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.2);
}

.stat-number {
    font-size: 1.5rem;
    font-weight: 700;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 0.25rem;
}

.stat-label {
    font-size: 0.8rem;
    color: #6b7280;
    font-weight: 500;
}

.about-content {
    padding-left: 2rem;
}

.about-description {
    color: #6b7280;
    font-size: 1.1rem;
    line-height: 1.7;
}

.about-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.mission-values-section {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    padding: 4rem 0;
}

.mission-vision-card {
    transition: all 0.3s ease;
    height: 100%;
}

.mission-vision-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
}

.mission-vision-icon {
    width: 70px;
    height: 70px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    transition: all 0.3s ease;
}

.mission-vision-icon i {
    font-size: 1.8rem;
    color: white;
}

.values-card {
    transition: all 0.3s ease;
    height: 100%;
}

.values-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
}

.values-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    transition: all 0.3s ease;
}

.values-icon i {
    font-size: 1.5rem;
    color: white;
}

.values-card:hover .values-icon {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.team-member-card {
    transition: all 0.3s ease;
    height: 100%;
}

.team-member-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.team-avatar-wrapper {
    position: relative;
    display: inline-block;
    margin-bottom: 1.5rem;
}

.team-avatar {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    object-fit: cover;
    border: 4px solid #667eea;
    transition: all 0.3s ease;
}

.team-status-badge {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 30px;
    height: 30px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 3px solid white;
}

.team-status-badge i {
    font-size: 0.75rem;
    color: white;
}

.team-social-links {
    display: flex;
    justify-content: center;
    gap: 0.75rem;
    opacity: 0.8;
    transform: translateY(5px);
    transition: all 0.3s ease;
}

.social-link {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.social-link.linkedin {
    background: #0077b5;
    color: white;
}

.social-link.twitter {
    background: #1da1f2;
    color: white;
}

.social-link.github {
    background: #333;
    color: white;
}

.social-link:hover {
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* About Page Mobile Responsive */
@media (max-width: 768px) {
    .about-featured-image {
        height: 300px;
    }

    .about-content {
        padding-left: 0;
        margin-top: 2rem;
    }

    .about-actions {
        justify-content: center;
    }

    .about-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.5rem;
    }

    .stat-number {
        font-size: 1.2rem;
    }

    .stat-label {
        font-size: 0.75rem;
    }

    .mission-values-section {
        padding: 3rem 0;
    }

    .mission-vision-icon {
        width: 60px;
        height: 60px;
    }

    .mission-vision-icon i {
        font-size: 1.5rem;
    }

    .values-icon {
        width: 50px;
        height: 50px;
    }

    .values-icon i {
        font-size: 1.2rem;
    }

    .team-avatar {
        width: 80px;
        height: 80px;
    }

    .team-status-badge {
        width: 25px;
        height: 25px;
    }

    .team-status-badge i {
        font-size: 0.6rem;
    }

    .social-link {
        width: 30px;
        height: 30px;
        font-size: 0.8rem;
    }
}

@media (max-width: 480px) {
    .about-featured-image {
        height: 250px;
    }

    .about-description {
        font-size: 1rem;
    }

    .about-actions {
        flex-direction: column;
        align-items: center;
    }

    .stat-number {
        font-size: 1rem;
    }

    .stat-label {
        font-size: 0.7rem;
    }

    .mission-vision-icon {
        width: 50px;
        height: 50px;
    }

    .mission-vision-icon i {
        font-size: 1.2rem;
    }

    .values-icon {
        width: 40px;
        height: 40px;
    }

    .values-icon i {
        font-size: 1rem;
    }

    .team-avatar {
        width: 70px;
        height: 70px;
    }

    .team-status-badge {
        width: 20px;
        height: 20px;
    }

    .team-status-badge i {
        font-size: 0.5rem;
    }

    .social-link {
        width: 28px;
        height: 28px;
        font-size: 0.75rem;
    }
}

/* Enhanced Home Page Styles - Premium Visual Experience */

/* Enhanced Hero Section */
.hero-enhanced {
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.hero-particles {
    position: absolute;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    animation: float-particle 20s infinite linear;
}

@keyframes float-particle {
    0% {
        transform: translateY(100vh) rotate(0deg);
        opacity: 0;
    }
    10% {
        opacity: 1;
    }
    90% {
        opacity: 1;
    }
    100% {
        transform: translateY(-100px) rotate(360deg);
        opacity: 0;
    }
}

.hero-gradient-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.8) 0%, rgba(118, 75, 162, 0.8) 100%);
}

.hero-content {
    position: relative;
    z-index: 3;
    color: white;
}

.hero-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 50px;
    padding: 0.5rem 1rem;
    margin-bottom: 2rem;
    font-size: 0.9rem;
    font-weight: 500;
}

.badge-icon {
    font-size: 1.2rem;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 800;
    line-height: 1.1;
    margin-bottom: 1.5rem;
}

.hero-highlight {
    background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 1.25rem;
    line-height: 1.6;
    opacity: 0.9;
    margin-bottom: 2rem;
    max-width: 600px;
}

.hero-stats {
    display: flex;
    gap: 2rem;
    margin-bottom: 2.5rem;
    flex-wrap: wrap;
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: #ffeaa7;
    display: block;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.8;
    margin-top: 0.25rem;
}

.hero-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.hero-visual {
    position: relative;
    z-index: 3;
}

.hero-image-container {
    position: relative;
    width: 400px;
    height: 400px;
    margin: 0 auto;
}

.hero-element-1 {
    background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
    top: 10%;
    left: 10%;
    animation: float 6s ease-in-out infinite;
}

.hero-element-2 {
    background: linear-gradient(135deg, #fd79a8 0%, #e84393 100%);
    top: 20%;
    right: 10%;
    animation: float 6s ease-in-out infinite 1.5s;
}

.hero-element-3 {
    background: linear-gradient(135deg, #fdcb6e 0%, #e17055 100%);
    bottom: 30%;
    left: 5%;
    animation: float 6s ease-in-out infinite 3s;
}

.hero-element-4 {
    background: linear-gradient(135deg, #55a3ff 0%, #003d82 100%);
    bottom: 10%;
    right: 15%;
    animation: float 6s ease-in-out infinite 4.5s;
}

.hero-center-graphic {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 200px;
    height: 200px;
}

.graphic-circle {
    width: 100%;
    height: 100%;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    animation: rotate 20s linear infinite;
}

.graphic-inner {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 120px;
    height: 120px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 3rem;
    color: white;
}

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.hero-scroll-indicator {
    position: absolute;
    bottom: 2rem;
    left: 50%;
    transform: translateX(-50%);
    z-index: 3;
    cursor: pointer;
    transition: opacity 0.3s ease;
}

.scroll-arrow {
    width: 40px;
    height: 40px;
    border: 2px solid rgba(255, 255, 255, 0.5);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

/* Enhanced Services Section */
.services-enhanced {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    padding: 6rem 0;
}

.service-card-enhanced {
    transition: none;
    height: 100%;
    overflow: hidden;
    transform-style: preserve-3d;
    perspective: 1000px;
    position: relative;
    cursor: pointer;
    will-change: transform;
}

.service-card-enhanced:hover {
    transform: none !important;
    transition: none !important;
    box-shadow:
        0 40px 80px rgba(102, 126, 234, 0.3),
        0 20px 40px rgba(118, 75, 162, 0.2),
        0 8px 25px rgba(0, 0, 0, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

/* Advanced Background Gradient Effects */
.service-card-enhanced::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg,
        rgba(102, 126, 234, 0.05) 0%,
        rgba(118, 75, 162, 0.08) 50%,
        rgba(240, 147, 251, 0.05) 100%);
    opacity: 0;
    transition: none;
    pointer-events: none;
    border-radius: 24px;
}

.service-card-enhanced:hover::after {
    opacity: 1;
}

/* Animated Border Gradient */
.service-card-enhanced::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg,
        #667eea, #764ba2, #f093fb, #667eea);
    background-size: 300% 300%;
    border-radius: 26px;
    opacity: 0;
    z-index: -1;
    transition: none;
    animation: gradient-shift 3s ease infinite;
}

.service-card-enhanced:hover::before {
    opacity: 1;
}

@keyframes gradient-shift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

.service-icon-wrapper {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    transition: none;
}

.service-icon {
    font-size: 2rem;
    color: white;
}

.service-icon-img {
    width: 40px;
    height: 40px;
    object-fit: contain;
    filter: brightness(0) invert(1);
}

/* Enhanced Services Section - Matching Project Card Design */
.service-image-wrapper {
    position: relative;
    height: 250px;
    overflow: hidden;
    border-radius: 16px 16px 0 0;
    background: linear-gradient(135deg, #dbeafe 0%, #f1f5f9 100%);
    display: flex;
    align-items: center;
    justify-content: center;
}

.service-icon-display {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: none;
    padding: 3rem;
    position: relative;
    overflow: hidden;
}

/* Icon Glow Effect */
.service-icon-display::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: radial-gradient(circle,
        rgba(102, 126, 234, 0.3) 0%,
        rgba(118, 75, 162, 0.2) 40%,
        transparent 70%);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: none;
    z-index: 1;
}

.service-card-enhanced:hover .service-icon-display::before {
    width: 250px;
    height: 250px;
}

/* Floating Particles Around Icon */
.service-icon-display::after {
    content: '';
    position: absolute;
    top: 20%;
    right: 20%;
    width: 8px;
    height: 8px;
    background: rgba(102, 126, 234, 0.6);
    border-radius: 50%;
    opacity: 0;
    transform: scale(0);
    transition: none;
}

.service-card-enhanced:hover .service-icon-display::after {
    opacity: 1;
    transform: scale(1) translateY(-20px);
    animation: float-particle 2s ease-in-out infinite;
}

.service-featured-icon {
    font-size: 5rem;
    color: #3b82f6;
    max-width: 100%;
    max-height: 100%;
    transition: none;
    position: relative;
    z-index: 2;
    filter: drop-shadow(0 0 0 transparent);
    transform-origin: center;
}

.service-card-enhanced:hover .service-featured-icon {
    color: #667eea;
    transform: scale(1.1);
    filter: drop-shadow(0 0 25px rgba(102, 126, 234, 0.5));
    animation: none;
}

@keyframes icon-bounce {
    0% { transform: scale(1) rotate(0deg); }
    30% { transform: scale(1.15) rotate(5deg); }
    60% { transform: scale(1.25) rotate(10deg); }
    100% { transform: scale(1.2) rotate(8deg); }
}

@keyframes float-particle {
    0%, 100% { transform: scale(1) translateY(-20px); opacity: 0.6; }
    50% { transform: scale(1.2) translateY(-30px); opacity: 1; }
}

.service-featured-icon img {
    width: 100%;
    height: 100%;
    max-width: 120px;
    max-height: 120px;
    object-fit: contain;
    filter: none;
}

.service-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.95) 0%, rgba(71, 85, 105, 0.95) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.service-overlay-content {
    padding: 1.5rem 1.5rem 1rem 1.5rem;
    text-align: center;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    gap: 0.5rem;
}

.service-overlay-description {
    font-size: 0.9rem;
    margin-bottom: 0;
    color: rgba(255, 255, 255, 0.95);
    line-height: 1.4;
    font-weight: 400;
    flex-grow: 1;
    text-align: center;
    max-height: calc(100% - 60px);
    overflow-y: auto;
    padding: 1rem 0;
    display: flex;
    align-items: flex-start;
    justify-content: center;
}

.service-overlay-content .modern-btn {
    flex-shrink: 0;
    margin-top: auto;
    align-self: center;
    margin-bottom: 0;
}

.service-overlay-content .service-price {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 0.5rem 0;
    flex-shrink: 0;
}

.service-overlay-content .price-label {
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 0.25rem;
}

.service-overlay-content .price-value {
    font-size: 1.25rem;
    font-weight: 700;
    color: #ffffff;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* Custom scrollbar for service overlay description */
.service-overlay-description::-webkit-scrollbar {
    width: 4px;
}

.service-overlay-description::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
}

.service-overlay-description::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 2px;
}

.service-overlay-description::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}

/* Service Card Content Styling */
.service-card-enhanced .card-body {
    padding: 1.5rem;
    text-align: center;
}

.service-card-enhanced .card-title {
    font-size: 1.3rem;
    font-weight: 700;
    color: #334155;
    margin-bottom: 0;
    line-height: 1.4;
    transition: none;
    position: relative;
    overflow: hidden;
    transform-origin: center;
}

.service-card-enhanced:hover .card-title {
    /* No effects on service name text */
}

/* Text Slide-in Effect - Disabled */
.service-card-enhanced .card-title::before {
    display: none;
}

.service-card-enhanced:hover .card-title::before {
    display: none;
}

/* Typewriter Cursor Effect - Disabled */
.service-card-enhanced .card-title::after {
    display: none;
}

.service-card-enhanced:hover .card-title::after {
    display: none;
}

@keyframes typewriter-blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0; }
}

/* Advanced Ripple Effect */
.service-card-enhanced .ripple {
    position: absolute;
    border-radius: 50%;
    background: radial-gradient(circle,
        rgba(102, 126, 234, 0.4) 0%,
        rgba(118, 75, 162, 0.3) 50%,
        transparent 70%);
    transform: scale(0);
    animation: ripple-animation 0.8s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    pointer-events: none;
    z-index: 10;
}

@keyframes ripple-animation {
    0% {
        transform: scale(0);
        opacity: 1;
    }
    100% {
        transform: scale(4);
        opacity: 0;
    }
}

/* Floating Elements for Enhanced Interaction */
.service-card-enhanced .floating-elements {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    overflow: hidden;
    opacity: 0;
    transition: opacity 0.6s ease;
}

.service-card-enhanced:hover .floating-elements {
    opacity: 1;
}

.service-card-enhanced .floating-dot {
    position: absolute;
    width: 6px;
    height: 6px;
    background: rgba(102, 126, 234, 0.7);
    border-radius: 50%;
    animation: float-dots 3s ease-in-out infinite;
}

.service-card-enhanced .floating-dot:nth-child(1) {
    top: 20%;
    left: 15%;
    animation-delay: 0s;
}

.service-card-enhanced .floating-dot:nth-child(2) {
    top: 60%;
    right: 20%;
    animation-delay: 1s;
}

.service-card-enhanced .floating-dot:nth-child(3) {
    bottom: 25%;
    left: 25%;
    animation-delay: 2s;
}

@keyframes float-dots {
    0%, 100% {
        transform: translateY(0) scale(1);
        opacity: 0.7;
    }
    50% {
        transform: translateY(-15px) scale(1.2);
        opacity: 1;
    }
}

/* Pulsing Animation for Card - Disabled */
.service-card-enhanced:hover {
    animation: none;
}

@keyframes service-pulse {
    0%, 100% {
        box-shadow:
            0 40px 80px rgba(102, 126, 234, 0.3),
            0 20px 40px rgba(118, 75, 162, 0.2),
            0 8px 25px rgba(0, 0, 0, 0.15),
            inset 0 1px 0 rgba(255, 255, 255, 0.1);
    }
    50% {
        box-shadow:
            0 45px 90px rgba(102, 126, 234, 0.35),
            0 25px 50px rgba(118, 75, 162, 0.25),
            0 12px 30px rgba(0, 0, 0, 0.2),
            inset 0 1px 0 rgba(255, 255, 255, 0.15);
    }
}

/* Performance Optimizations */
.service-card-enhanced.will-change-transform {
    will-change: transform, box-shadow, opacity;
}

.service-card-enhanced .service-icon-display.will-change-transform {
    will-change: transform, filter;
}

.service-card-enhanced .card-title.will-change-transform {
    will-change: transform, color, text-shadow;
}

/* GPU Acceleration */
.service-card-enhanced,
.service-icon-display,
.service-featured-icon,
.card-title {
    transform: translateZ(0);
    backface-visibility: hidden;
}

/* Touch Device Support */
.service-card-enhanced.touch-active {
    transform: none;
    box-shadow:
        0 35px 70px rgba(102, 126, 234, 0.25),
        0 18px 35px rgba(118, 75, 162, 0.18),
        0 8px 20px rgba(0, 0, 0, 0.12);
}

.service-card-enhanced.touch-active .service-featured-icon {
    color: #667eea;
    transform: scale(1.1);
}

.service-card-enhanced.touch-active .card-title {
    /* No effects on service name text */
}

/* Accessibility and Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    .service-card-enhanced,
    .service-card-enhanced *,
    .service-card-enhanced::before,
    .service-card-enhanced::after {
        animation: none !important;
        transition: none !important;
    }

    .service-card-enhanced:hover {
        transform: none;
        box-shadow: 0 15px 30px rgba(102, 126, 234, 0.2);
    }

    .service-card-enhanced:hover .service-featured-icon {
        transform: scale(1.1);
        filter: none;
    }

    .service-card-enhanced .floating-elements,
    .service-card-enhanced .ripple {
        display: none;
    }
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    .service-card-enhanced {
        border: 2px solid #000;
    }

    .service-card-enhanced:hover {
        border-color: #667eea;
    }

    .service-featured-icon {
        color: #000 !important;
    }

    .service-card-enhanced:hover .service-featured-icon {
        color: #667eea !important;
    }
}

/* Focus Styles for Keyboard Navigation */
.service-card-enhanced:focus-visible {
    outline: 3px solid #667eea;
    outline-offset: 3px;
    transform: none;
}

.service-card-enhanced:focus-visible .service-featured-icon {
    color: #667eea;
    transform: scale(1.15);
}

/* Enhanced Projects Section */
.projects-enhanced {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    padding: 6rem 0;
}

.project-card-enhanced {
    transition: all 0.4s ease;
    height: 100%;
    overflow: hidden;
}

.project-card-enhanced:hover {
    transform: translateY(-5px);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
}

.project-image-wrapper {
    position: relative;
    overflow: hidden;
    border-radius: 16px 16px 0 0;
    height: 250px;
}

.project-featured-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.project-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.9) 0%, rgba(118, 75, 162, 0.9) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.project-overlay-content {
    text-align: center;
    color: white;
    padding: 2rem;
}

.project-overlay-title {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.project-overlay-description {
    margin-bottom: 1.5rem;
    opacity: 0.9;
}

.project-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid #f1f5f9;
}

.project-status {
    background: linear-gradient(135deg, #00b894 0%, #00a085 100%);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
}

.project-date {
    color: #6b7280;
    font-size: 0.9rem;
}

/* Enhanced Technologies Section */
.technologies-enhanced {
    padding: 6rem 0;
}

.tech-carousel-enhanced {
    overflow: hidden;
    margin: 3rem 0;
}

.tech-track-enhanced {
    display: flex;
    gap: 2rem;
    animation: scroll-tech 30s linear infinite;
}

.tech-item-enhanced {
    flex-shrink: 0;
}

.tech-card {
    background: white;
    border-radius: 16px;
    padding: 1.5rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    text-align: center;
    transition: all 0.3s ease;
    min-width: 150px;
}

.tech-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.tech-logo-enhanced {
    width: 60px;
    height: 60px;
    object-fit: contain;
    margin-bottom: 1rem;
}

.tech-name {
    font-weight: 600;
    color: #374151;
    font-size: 0.9rem;
}

@keyframes scroll-tech {
    0% {
        transform: translateX(0);
    }
    100% {
        transform: translateX(-50%);
    }
}

/* Enhanced Testimonials Section */
.testimonials-enhanced {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    padding: 6rem 0;
}

.testimonial-card-enhanced {
    transition: all 0.4s ease;
    height: 100%;
}

.testimonial-card-enhanced:hover {
    transform: translateY(-5px);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
}

.testimonial-rating {
    display: flex;
    gap: 0.25rem;
    margin-bottom: 1.5rem;
    justify-content: center;
}

.testimonial-rating .fas.fa-star {
    color: #fbbf24;
}

.testimonial-rating .far.fa-star {
    color: #d1d5db;
}

.testimonial-content {
    position: relative;
    margin-bottom: 2rem;
}

.testimonial-quote-icon {
    position: absolute;
    top: -10px;
    left: -10px;
    font-size: 2rem;
    color: #667eea;
    opacity: 0.3;
}

.testimonial-text {
    font-style: italic;
    line-height: 1.7;
    color: #374151;
    padding-left: 1rem;
}

.testimonial-author {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.testimonial-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid #667eea;
    transition: transform 0.3s ease;
}

.testimonial-info {
    flex: 1;
}

.author-name {
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 0.25rem;
}

.author-title {
    color: #6b7280;
    font-size: 0.9rem;
    margin-bottom: 0.25rem;
}

.author-company {
    color: #667eea;
    font-size: 0.9rem;
    font-weight: 500;
}

/* Enhanced CTA Section */
.cta-card-enhanced {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    position: relative;
    overflow: hidden;
}

.cta-card-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.1;
}

.cta-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
    justify-content: center;
}

/* Enhanced Button Styles */
.modern-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border-radius: 12px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    border: none;
    cursor: pointer;
}

.modern-btn.large {
    padding: 1rem 2rem;
    font-size: 1.1rem;
}

.modern-btn.small {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
}

.modern-btn.primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.modern-btn.primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
    color: white;
    text-decoration: none;
}

.modern-btn.secondary {
    background: white;
    color: #667eea;
    border: 2px solid #667eea;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.modern-btn.secondary:hover {
    background: #667eea;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    text-decoration: none;
}

.btn-icon {
    transition: transform 0.3s ease;
}

.modern-btn:hover .btn-icon {
    transform: translateX(3px);
}

/* Animation Classes */
.animate-on-scroll {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.8s ease;
}

.animate-on-scroll.animate-in {
    opacity: 1;
    transform: translateY(0);
}

/* Enhanced Empty State */
.empty-state-card {
    text-align: center;
    padding: 3rem 2rem;
}

.empty-state-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 2rem;
    color: white;
}

/* Mobile Responsive Enhancements */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }

    .hero-subtitle {
        font-size: 1.1rem;
    }

    .hero-stats {
        justify-content: center;
        gap: 1.5rem;
    }

    .stat-number {
        font-size: 1.5rem;
    }

    .hero-actions {
        justify-content: center;
    }

    .hero-image-container {
        width: 300px;
        height: 300px;
    }

    .graphic-inner {
        width: 100px;
        height: 100px;
        font-size: 2rem;
    }

    .services-enhanced,
    .projects-enhanced,
    .technologies-enhanced,
    .testimonials-enhanced {
        padding: 4rem 0;
    }

    .service-icon-wrapper {
        width: 60px;
        height: 60px;
    }

    .service-icon {
        font-size: 1.5rem;
    }

    .service-image-wrapper {
        height: 200px;
    }

    .service-icon-display {
        padding: 2.5rem;
    }

    .service-featured-icon {
        font-size: 4rem;
    }

    .service-featured-icon img {
        max-width: 100px;
        max-height: 100px;
    }

    .service-overlay-content {
        padding: 1rem 1rem 0.75rem 1rem;
        gap: 0.25rem;
    }

    .service-overlay-description {
        font-size: 0.85rem;
        margin-bottom: 0;
        line-height: 1.3;
    }

    .project-image-wrapper {
        height: 200px;
    }

    .tech-logo-enhanced {
        width: 50px;
        height: 50px;
    }

    .testimonial-avatar {
        width: 50px;
        height: 50px;
    }

    .cta-actions {
        flex-direction: column;
        align-items: center;
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 2rem;
    }

    .hero-subtitle {
        font-size: 1rem;
    }

    .hero-stats {
        gap: 1rem;
    }

    .stat-number {
        font-size: 1.2rem;
    }

    .hero-image-container {
        width: 250px;
        height: 250px;
    }

    .graphic-inner {
        width: 80px;
        height: 80px;
        font-size: 1.5rem;
    }

    .services-enhanced,
    .projects-enhanced,
    .technologies-enhanced,
    .testimonials-enhanced {
        padding: 3rem 0;
    }

    .service-icon-wrapper {
        width: 50px;
        height: 50px;
    }

    .service-icon {
        font-size: 1.2rem;
    }

    .service-image-wrapper {
        height: 180px;
    }

    .service-icon-display {
        padding: 2rem;
    }

    .service-featured-icon {
        font-size: 3rem;
    }

    .service-featured-icon img {
        max-width: 80px;
        max-height: 80px;
    }

    .service-overlay-content {
        padding: 0.75rem 0.75rem 0.5rem 0.75rem;
        gap: 0.25rem;
    }

    .service-overlay-description {
        font-size: 0.8rem;
        margin-bottom: 0;
        line-height: 1.3;
    }

    .project-image-wrapper {
        height: 180px;
    }

    .tech-card {
        min-width: 120px;
        padding: 1rem;
    }

    .tech-logo-enhanced {
        width: 40px;
        height: 40px;
    }

    .testimonial-avatar {
        width: 40px;
        height: 40px;
    }
}

/* Force disable service card hover transforms and transitions only */
.service-card-enhanced:hover,
.service-card-enhanced:focus,
.service-card-enhanced:active,
.service-card-enhanced.touch-active,
.service-card-enhanced:focus-visible {
    transform: none !important;
    transition: none !important;
}

/* Override any hover transforms that might be applied */
.service-card-enhanced:hover {
    transform: none !important;
}

/* Allow entrance animations for service cards */
.service-card-enhanced.animate-on-scroll {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.8s ease;
}

.service-card-enhanced.animate-on-scroll.animate-in {
    opacity: 1;
    transform: translateY(0);
}

/* Ensure entrance animation takes precedence over hover restrictions */
.service-card-enhanced.animate-on-scroll:not(.animate-in) {
    opacity: 0 !important;
    transform: translateY(30px) !important;
    transition: all 0.8s ease !important;
}
